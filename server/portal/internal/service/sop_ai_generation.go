package service

import (
	"context"
	"errors"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/cloudwego/eino/schema"
	"go.uber.org/zap"

	"navy-ng/models/portal"
)

// performAIGeneration performs the actual AI generation process.
func (s *SOPService) performAIGeneration(ctx context.Context, historyID int, template *SOPTemplateResponse, req SOPGenerateRequest, stream<PERSON>han chan<- SOPGenerateStreamResponse) {
	defer close(streamChan)

	startTime := time.Now()

	// Update status to generating
	s.updateGenerationStatus(ctx, historyID, "generating", 10, "")

	// Send initial progress
	streamChan <- SOPGenerateStreamResponse{
		Type:      "progress",
		Progress:  10,
		HistoryID: historyID,
	}

	// Build prompt from template and object info
	prompt, err := s.buildPrompt(template, req.ObjectInfo)
	if err != nil {
		s.handleGenerationError(ctx, historyID, streamChan, fmt.Sprintf("Failed to build prompt: %v", err))
		return
	}

	// Update progress
	s.updateGenerationStatus(ctx, historyID, "generating", 20, "")
	streamChan <- SOPGenerateStreamResponse{
		Type:      "progress",
		Progress:  20,
		HistoryID: historyID,
	}

	// Check if AI model is available
	if s.aiModel == nil {
		s.handleGenerationError(ctx, historyID, streamChan, "AI model not configured")
		return
	}

	// Prepare chat messages
	messages := []*schema.Message{
		{
			Role:    schema.System,
			Content: s.getSystemPrompt(),
		},
		{
			Role:    schema.User,
			Content: prompt,
		},
	}

	// Update progress
	s.updateGenerationStatus(ctx, historyID, "generating", 30, "")
	streamChan <- SOPGenerateStreamResponse{
		Type:      "progress",
		Progress:  30,
		HistoryID: historyID,
	}

	// Generate content using AI model
	var generatedContent strings.Builder
	var tokenCount int

	// Update progress
	s.updateGenerationStatus(ctx, historyID, "generating", 50, "")
	streamChan <- SOPGenerateStreamResponse{
		Type:      "progress",
		Progress:  50,
		HistoryID: historyID,
	}

	// Execute AI generation with streaming
	sr, err := s.aiModel.Stream(ctx, messages)
	if err != nil {
		s.handleGenerationError(ctx, historyID, streamChan, fmt.Sprintf("AI generation failed: %v", err))
		return
	}

	// Process streaming response
	for {
		msgChunk, recvErr := sr.Recv()
		if errors.Is(recvErr, io.EOF) {
			break
		}
		if recvErr != nil {
			s.handleGenerationError(ctx, historyID, streamChan, fmt.Sprintf("Stream receive failed: %v", recvErr))
			return
		}

		if msgChunk.Content != "" {
			generatedContent.WriteString(msgChunk.Content)

			// Send content chunk to client
			streamChan <- SOPGenerateStreamResponse{
				Type:      "content",
				Content:   msgChunk.Content,
				HistoryID: historyID,
			}
		}

		// Update token count if available
		if msgChunk.ResponseMeta != nil && msgChunk.ResponseMeta.Usage != nil {
			tokenCount = msgChunk.ResponseMeta.Usage.TotalTokens
		}
	}

	// Update progress
	s.updateGenerationStatus(ctx, historyID, "generating", 80, "")
	streamChan <- SOPGenerateStreamResponse{
		Type:      "progress",
		Progress:  80,
		HistoryID: historyID,
	}

	// Process generated content
	finalContent := generatedContent.String()
	if finalContent == "" {
		s.handleGenerationError(ctx, historyID, streamChan, "No content generated")
		return
	}

	// Calculate duration
	duration := time.Since(startTime)

	// Save document if requested
	var documentID int
	if req.SaveAsDocument {
		doc, createErr := s.CreateDocument(ctx, SOPDocumentCreateDTO{
			Title:       req.DocumentTitle,
			Content:     finalContent,
			ContentType: template.ContentType,
			Status:      "draft",
			Version:     "1.0",
			Category:    template.Category,
			Author:      req.ObjectInfo.Operator,
			Description: fmt.Sprintf("Generated from template: %s", template.Name),
		})
		if createErr != nil {
			s.logger.Warn("Failed to save generated document", zap.Error(createErr))
		} else {
			documentID = doc.ID
		}
	}

	// Update generation history with final results
	err = s.db.WithContext(ctx).Model(&portal.SOPGenerationHistory{}).
		Where("id = ?", historyID).
		Updates(map[string]interface{}{
			"document_id": documentID,
			"status":      "completed",
			"progress":    100,
			"duration":    int(duration.Milliseconds()),
			"tokens_used": tokenCount,
			"prompt":      prompt,
		}).Error

	if err != nil {
		s.logger.Error("Failed to update generation history", zap.Error(err))
	}

	// Increment template usage count
	s.db.WithContext(ctx).Model(&portal.SOPTemplate{}).
		Where("id = ?", req.TemplateID).
		UpdateColumn("usage_count", "usage_count + 1")

	// Send completion signal
	streamChan <- SOPGenerateStreamResponse{
		Type:      "complete",
		Progress:  100,
		HistoryID: historyID,
	}
}

// buildPrompt builds the AI prompt from template and object information.
func (s *SOPService) buildPrompt(template *SOPTemplateResponse, objectInfo portal.SOPObjectInfo) (string, error) {
	var promptBuilder strings.Builder

	// Add context information
	promptBuilder.WriteString("请基于以下模板和操作对象信息，生成详细的标准操作程序(SOP)文档：\n\n")

	// Add template information
	promptBuilder.WriteString("## 模板信息\n")
	promptBuilder.WriteString(fmt.Sprintf("模板名称: %s\n", template.Name))
	if template.Description != "" {
		promptBuilder.WriteString(fmt.Sprintf("模板描述: %s\n", template.Description))
	}
	promptBuilder.WriteString(fmt.Sprintf("分类: %s\n", template.Category))
	promptBuilder.WriteString("\n")

	// Add template content
	promptBuilder.WriteString("## 模板内容\n")
	promptBuilder.WriteString(template.Content)
	promptBuilder.WriteString("\n\n")

	// Add object information
	promptBuilder.WriteString("## 操作对象信息\n")
	promptBuilder.WriteString(fmt.Sprintf("目标对象: %s\n", objectInfo.TargetName))

	if objectInfo.BatchInfo != "" {
		promptBuilder.WriteString(fmt.Sprintf("批次信息: %s\n", objectInfo.BatchInfo))
	}
	if objectInfo.Environment != "" {
		promptBuilder.WriteString(fmt.Sprintf("环境: %s\n", objectInfo.Environment))
	}
	if objectInfo.Operator != "" {
		promptBuilder.WriteString(fmt.Sprintf("操作员: %s\n", objectInfo.Operator))
	}
	if objectInfo.Department != "" {
		promptBuilder.WriteString(fmt.Sprintf("部门: %s\n", objectInfo.Department))
	}
	if objectInfo.ContactInfo != "" {
		promptBuilder.WriteString(fmt.Sprintf("联系信息: %s\n", objectInfo.ContactInfo))
	}

	// Add custom parameters
	if len(objectInfo.CustomParameters) > 0 {
		promptBuilder.WriteString("\n### 自定义参数\n")
		for key, value := range objectInfo.CustomParameters {
			promptBuilder.WriteString(fmt.Sprintf("%s: %v\n", key, value))
		}
	}

	// Add template variables if available
	if len(template.Variables.Variables) > 0 {
		promptBuilder.WriteString("\n## 模板变量\n")
		for _, variable := range template.Variables.Variables {
			promptBuilder.WriteString(fmt.Sprintf("- %s (%s): %s\n", variable.Name, variable.Type, variable.Description))
			if variable.DefaultValue != nil {
				promptBuilder.WriteString(fmt.Sprintf("  默认值: %v\n", variable.DefaultValue))
			}
		}
	}

	promptBuilder.WriteString("\n## 生成要求\n")
	promptBuilder.WriteString("1. 请根据模板内容和操作对象信息，生成具体的、可执行的SOP文档\n")
	promptBuilder.WriteString("2. 确保所有步骤都具体明确，包含必要的检查点和注意事项\n")
	promptBuilder.WriteString("3. 根据操作对象的具体信息，替换模板中的占位符和变量\n")
	promptBuilder.WriteString("4. 保持专业的技术文档格式\n")
	promptBuilder.WriteString("5. 如果模板内容是HTML格式，请保持HTML格式；如果是Markdown格式，请保持Markdown格式\n")

	return promptBuilder.String(), nil
}

// getSystemPrompt returns the system prompt for AI generation.
func (s *SOPService) getSystemPrompt() string {
	return `你是一个专业的技术文档编写助手，专门负责生成标准操作程序(SOP)文档。

你的任务是：
1. 根据提供的模板和操作对象信息，生成详细、准确、可执行的SOP文档
2. 确保生成的文档结构清晰，步骤明确，包含必要的安全提示和检查点
3. 根据具体的操作对象信息，个性化定制文档内容
4. 保持专业的技术文档风格，使用准确的技术术语
5. 确保文档的实用性和可操作性

请严格按照用户提供的模板格式和要求生成文档。`
}

// updateGenerationStatus updates the generation status in database.
func (s *SOPService) updateGenerationStatus(ctx context.Context, historyID int, status string, progress int, errorMsg string) {
	updates := map[string]interface{}{
		"status":   status,
		"progress": progress,
	}
	if errorMsg != "" {
		updates["error_message"] = errorMsg
	}

	err := s.db.WithContext(ctx).Model(&portal.SOPGenerationHistory{}).
		Where("id = ?", historyID).
		Updates(updates).Error

	if err != nil {
		s.logger.Error("Failed to update generation status", zap.Error(err))
	}
}

// handleGenerationError handles errors during generation process.
func (s *SOPService) handleGenerationError(ctx context.Context, historyID int, streamChan chan<- SOPGenerateStreamResponse, errorMsg string) {
	s.updateGenerationStatus(ctx, historyID, "failed", 0, errorMsg)

	streamChan <- SOPGenerateStreamResponse{
		Type:      "error",
		Error:     errorMsg,
		HistoryID: historyID,
	}
}

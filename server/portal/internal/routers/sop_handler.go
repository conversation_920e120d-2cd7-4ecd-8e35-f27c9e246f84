package routers

import (
	"fmt"
	"navy-ng/pkg/middleware/render"
	"navy-ng/server/portal/internal/service"
	"navy-ng/server/portal/internal/services"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// SOPHandler handles HTTP requests related to SOP documents and templates.
type SOPHandler struct {
	service *service.SOPService
}

// NewSOPHandler creates a new SOPHandler.
func NewSOPHandler(db *gorm.DB, logger *zap.Logger) *SOPHandler {
	sopService := service.NewSOPService(db, logger)
	return &SOPHandler{service: sopService}
}

// RegisterRoutes registers SOP routes with the given router group.
func (h *SOPHandler) RegisterRoutes(r *gin.RouterGroup) {
	sopGroup := r.Group(RouteGroupSOP)
	{
		// Document routes
		sopGroup.GET("/documents", h.listDocuments)
		sopGroup.GET("/documents/:id", h.getDocument)
		sopGroup.POST("/documents", h.createDocument)
		sopGroup.PUT("/documents/:id", h.updateDocument)
		sopGroup.DELETE("/documents/:id", h.deleteDocument)

		// Template routes
		sopGroup.GET("/templates", h.listTemplates)
		sopGroup.GET("/templates/:id", h.getTemplate)
		sopGroup.POST("/templates", h.createTemplate)
		sopGroup.PUT("/templates/:id", h.updateTemplate)
		sopGroup.DELETE("/templates/:id", h.deleteTemplate)

		// AI Generation routes
		sopGroup.POST("/generate", h.generateContent)
		// TODO: Implement generation history endpoint
		// sopGroup.GET("/generate/stream/:historyId", h.streamGeneration)

		// Template Scraping routes
		sopGroup.POST("/scrape", h.scrapeTemplate)
		sopGroup.GET("/scrape/selectors", h.getSupportedSelectors)
	}
}

// listDocuments handles GET /sop/documents
// @Summary List SOP documents
// @Description Get a paginated list of SOP documents
// @Tags SOP
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param size query int false "Page size" default(10)
// @Param title query string false "Filter by title"
// @Param status query string false "Filter by status"
// @Success 200 {object} service.SOPDocumentListResponse
// @Failure 400 {object} render.ErrorResponse
// @Failure 500 {object} render.ErrorResponse
// @Router /sop/documents [get]
func (h *SOPHandler) listDocuments(c *gin.Context) {
	// Parse query parameters
	var query service.SOPDocumentQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		render.BadRequest(c, fmt.Sprintf(MsgInvalidQueryParams, err.Error()))
		return
	}

	// Set defaults
	if query.Page <= 0 {
		query.Page = DefaultPageInt
	}
	if query.Size <= 0 {
		query.Size = DefaultSizeInt
	}
	if query.Size > MaxSizeInt {
		query.Size = MaxSizeInt
	}

	// Call service
	response, err := h.service.ListDocuments(c.Request.Context(), query)
	if err != nil {
		render.InternalServerError(c, fmt.Sprintf(MsgFailedToListSOPDocuments, err.Error()))
		return
	}

	render.Success(c, response)
}

// getDocument handles GET /sop/documents/:id
// @Summary Get SOP document by ID
// @Description Get a specific SOP document by its ID
// @Tags SOP
// @Accept json
// @Produce json
// @Param id path string true "Document ID"
// @Success 200 {object} service.SOPDocumentResponse
// @Failure 400 {object} render.ErrorResponse
// @Failure 404 {object} render.ErrorResponse
// @Failure 500 {object} render.ErrorResponse
// @Router /sop/documents/{id} [get]
func (h *SOPHandler) getDocument(c *gin.Context) {
	idStr := c.Param(ParamID)
	if idStr == "" {
		render.BadRequest(c, MsgInvalidID)
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		render.BadRequest(c, MsgInvalidIDFormat)
		return
	}

	response, err := h.service.GetDocument(c.Request.Context(), id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			render.NotFound(c, MsgSOPDocumentNotFound)
			return
		}
		render.InternalServerError(c, fmt.Sprintf(MsgFailedToGetSOPDocument, err.Error()))
		return
	}

	render.Success(c, response)
}

// createDocument handles POST /sop/documents
// @Summary Create SOP document
// @Description Create a new SOP document
// @Tags SOP
// @Accept json
// @Produce json
// @Param request body service.SOPDocumentCreateDTO true "Document creation request"
// @Success 201 {object} service.SOPDocumentResponse
// @Failure 400 {object} render.ErrorResponse
// @Failure 500 {object} render.ErrorResponse
// @Router /sop/documents [post]
func (h *SOPHandler) createDocument(c *gin.Context) {
	var request service.SOPDocumentCreateDTO
	if err := c.ShouldBindJSON(&request); err != nil {
		render.BadRequest(c, fmt.Sprintf(MsgInvalidRequestBody, err.Error()))
		return
	}

	response, err := h.service.CreateDocument(c.Request.Context(), request)
	if err != nil {
		render.InternalServerError(c, fmt.Sprintf(MsgFailedToCreateSOPDocument, err.Error()))
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"code":    http.StatusCreated,
		"message": MsgSOPDocumentCreatedSuccess,
		"data":    response,
	})
}

// updateDocument handles PUT /sop/documents/:id
// @Summary Update SOP document
// @Description Update an existing SOP document
// @Tags SOP
// @Accept json
// @Produce json
// @Param id path string true "Document ID"
// @Param request body service.SOPDocumentUpdateDTO true "Document update request"
// @Success 200 {object} service.SOPDocumentResponse
// @Failure 400 {object} render.ErrorResponse
// @Failure 404 {object} render.ErrorResponse
// @Failure 500 {object} render.ErrorResponse
// @Router /sop/documents/{id} [put]
func (h *SOPHandler) updateDocument(c *gin.Context) {
	idStr := c.Param(ParamID)
	if idStr == "" {
		render.BadRequest(c, MsgInvalidID)
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		render.BadRequest(c, MsgInvalidIDFormat)
		return
	}

	var request service.SOPDocumentUpdateDTO
	if err := c.ShouldBindJSON(&request); err != nil {
		render.BadRequest(c, fmt.Sprintf(MsgInvalidRequestBody, err.Error()))
		return
	}

	response, err := h.service.UpdateDocument(c.Request.Context(), id, request)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			render.NotFound(c, MsgSOPDocumentNotFound)
			return
		}
		render.InternalServerError(c, fmt.Sprintf(MsgFailedToUpdateSOPDocument, err.Error()))
		return
	}

	render.Success(c, response)
}

// deleteDocument handles DELETE /sop/documents/:id
// @Summary Delete SOP document
// @Description Delete an existing SOP document
// @Tags SOP
// @Accept json
// @Produce json
// @Param id path string true "Document ID"
// @Success 200 {object} render.SuccessResponse
// @Failure 400 {object} render.ErrorResponse
// @Failure 404 {object} render.ErrorResponse
// @Failure 500 {object} render.ErrorResponse
// @Router /sop/documents/{id} [delete]
func (h *SOPHandler) deleteDocument(c *gin.Context) {
	idStr := c.Param(ParamID)
	if idStr == "" {
		render.BadRequest(c, MsgInvalidID)
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		render.BadRequest(c, MsgInvalidIDFormat)
		return
	}

	err = h.service.DeleteDocument(c.Request.Context(), id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			render.NotFound(c, MsgSOPDocumentNotFound)
			return
		}
		render.InternalServerError(c, fmt.Sprintf(MsgFailedToDeleteSOPDocument, err.Error()))
		return
	}

	render.Success(c, gin.H{"message": MsgSOPDocumentDeletedSuccess})
}

// listTemplates handles GET /sop/templates
// @Summary List SOP templates
// @Description Get a paginated list of SOP templates
// @Tags SOP
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param size query int false "Page size" default(10)
// @Param name query string false "Filter by name"
// @Param category query string false "Filter by category"
// @Success 200 {object} service.SOPTemplateListResponse
// @Failure 400 {object} render.ErrorResponse
// @Failure 500 {object} render.ErrorResponse
// @Router /sop/templates [get]
func (h *SOPHandler) listTemplates(c *gin.Context) {
	// Parse query parameters
	var query service.SOPTemplateQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		render.BadRequest(c, fmt.Sprintf(MsgInvalidQueryParams, err.Error()))
		return
	}

	// Set defaults
	if query.Page <= 0 {
		query.Page = DefaultPageInt
	}
	if query.Size <= 0 {
		query.Size = DefaultSizeInt
	}
	if query.Size > MaxSizeInt {
		query.Size = MaxSizeInt
	}

	// Call service
	response, err := h.service.ListTemplates(c.Request.Context(), query)
	if err != nil {
		render.InternalServerError(c, fmt.Sprintf(MsgFailedToListSOPTemplates, err.Error()))
		return
	}

	render.Success(c, response)
}

// getTemplate handles GET /sop/templates/:id
// @Summary Get SOP template by ID
// @Description Get a specific SOP template by its ID
// @Tags SOP
// @Accept json
// @Produce json
// @Param id path string true "Template ID"
// @Success 200 {object} service.SOPTemplateResponse
// @Failure 400 {object} render.ErrorResponse
// @Failure 404 {object} render.ErrorResponse
// @Failure 500 {object} render.ErrorResponse
// @Router /sop/templates/{id} [get]
func (h *SOPHandler) getTemplate(c *gin.Context) {
	idStr := c.Param(ParamID)
	if idStr == "" {
		render.BadRequest(c, MsgInvalidID)
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		render.BadRequest(c, MsgInvalidIDFormat)
		return
	}

	response, err := h.service.GetTemplate(c.Request.Context(), id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			render.NotFound(c, MsgSOPTemplateNotFound)
			return
		}
		render.InternalServerError(c, fmt.Sprintf(MsgFailedToGetSOPTemplate, err.Error()))
		return
	}

	render.Success(c, response)
}

// createTemplate handles POST /sop/templates
// @Summary Create SOP template
// @Description Create a new SOP template
// @Tags SOP
// @Accept json
// @Produce json
// @Param request body service.SOPTemplateCreateDTO true "Template creation request"
// @Success 201 {object} service.SOPTemplateResponse
// @Failure 400 {object} render.ErrorResponse
// @Failure 500 {object} render.ErrorResponse
// @Router /sop/templates [post]
func (h *SOPHandler) createTemplate(c *gin.Context) {
	var request service.SOPTemplateCreateDTO
	if err := c.ShouldBindJSON(&request); err != nil {
		render.BadRequest(c, fmt.Sprintf(MsgInvalidRequestBody, err.Error()))
		return
	}

	response, err := h.service.CreateTemplate(c.Request.Context(), request)
	if err != nil {
		render.InternalServerError(c, fmt.Sprintf(MsgFailedToCreateSOPTemplate, err.Error()))
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"code":    http.StatusCreated,
		"message": MsgSOPTemplateCreatedSuccess,
		"data":    response,
	})
}

// updateTemplate handles PUT /sop/templates/:id
// @Summary Update SOP template
// @Description Update an existing SOP template
// @Tags SOP
// @Accept json
// @Produce json
// @Param id path string true "Template ID"
// @Param request body service.SOPTemplateUpdateDTO true "Template update request"
// @Success 200 {object} service.SOPTemplateResponse
// @Failure 400 {object} render.ErrorResponse
// @Failure 404 {object} render.ErrorResponse
// @Failure 500 {object} render.ErrorResponse
// @Router /sop/templates/{id} [put]
func (h *SOPHandler) updateTemplate(c *gin.Context) {
	idStr := c.Param(ParamID)
	if idStr == "" {
		render.BadRequest(c, MsgInvalidID)
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		render.BadRequest(c, MsgInvalidIDFormat)
		return
	}

	var request service.SOPTemplateUpdateDTO
	if err := c.ShouldBindJSON(&request); err != nil {
		render.BadRequest(c, fmt.Sprintf(MsgInvalidRequestBody, err.Error()))
		return
	}

	response, err := h.service.UpdateTemplate(c.Request.Context(), id, request)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			render.NotFound(c, MsgSOPTemplateNotFound)
			return
		}
		render.InternalServerError(c, fmt.Sprintf(MsgFailedToUpdateSOPTemplate, err.Error()))
		return
	}

	render.Success(c, response)
}

// deleteTemplate handles DELETE /sop/templates/:id
// @Summary Delete SOP template
// @Description Delete an existing SOP template
// @Tags SOP
// @Accept json
// @Produce json
// @Param id path string true "Template ID"
// @Success 200 {object} render.SuccessResponse
// @Failure 400 {object} render.ErrorResponse
// @Failure 404 {object} render.ErrorResponse
// @Failure 500 {object} render.ErrorResponse
// @Router /sop/templates/{id} [delete]
func (h *SOPHandler) deleteTemplate(c *gin.Context) {
	idStr := c.Param(ParamID)
	if idStr == "" {
		render.BadRequest(c, MsgInvalidID)
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		render.BadRequest(c, MsgInvalidIDFormat)
		return
	}

	err = h.service.DeleteTemplate(c.Request.Context(), id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			render.NotFound(c, MsgSOPTemplateNotFound)
			return
		}
		render.InternalServerError(c, fmt.Sprintf(MsgFailedToDeleteSOPTemplate, err.Error()))
		return
	}

	render.Success(c, gin.H{"message": MsgSOPTemplateDeletedSuccess})
}

// generateContent handles POST /sop/generate
// @Summary Generate SOP content using AI
// @Description Generate SOP content based on template and object information
// @Tags SOP
// @Accept json
// @Produce json
// @Param request body service.SOPGenerateRequest true "Generation request"
// @Success 200 {object} service.SOPGenerateResponse
// @Failure 400 {object} render.ErrorResponse
// @Failure 500 {object} render.ErrorResponse
// @Router /sop/generate [post]
func (h *SOPHandler) generateContent(c *gin.Context) {
	var request service.SOPGenerateRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		render.BadRequest(c, fmt.Sprintf(MsgInvalidSOPGenerateRequest, err.Error()))
		return
	}

	// Create a channel for streaming responses
	streamChan := make(chan service.SOPGenerateStreamResponse, 100)
	defer close(streamChan)

	// Start generation in a goroutine
	go func() {
		_, err := h.service.GenerateSOPContent(c.Request.Context(), request, streamChan)
		if err != nil {
			// Send error to stream
			streamChan <- service.SOPGenerateStreamResponse{
				Type:  "error",
				Error: err.Error(),
			}
		}
	}()

	// Set headers for Server-Sent Events
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")

	// Stream responses
	for response := range streamChan {
		c.SSEvent("message", response)
		c.Writer.Flush()

		// Break on completion or error
		if response.Type == "complete" || response.Type == "error" {
			break
		}
	}
}

// TODO: Implement streamGeneration method when GetGenerationHistory is available in service

// scrapeTemplate handles template scraping requests
func (h *SOPHandler) scrapeTemplate(c *gin.Context) {
	var req struct {
		URL             string            `json:"url" binding:"required"`
		WaitForSelector string            `json:"wait_for_selector,omitempty"`
		RemoveSelectors []string          `json:"remove_selectors,omitempty"`
		ContentSelector string            `json:"content_selector,omitempty"`
		TitleSelector   string            `json:"title_selector,omitempty"`
		Timeout         int               `json:"timeout,omitempty"`
		TakeScreenshot  bool              `json:"take_screenshot,omitempty"`
		CustomHeaders   map[string]string `json:"custom_headers,omitempty"`
		UserAgent       string            `json:"user_agent,omitempty"`
		ViewportWidth   int               `json:"viewport_width,omitempty"`
		ViewportHeight  int               `json:"viewport_height,omitempty"`
		WaitTime        int               `json:"wait_time,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		render.BadRequest(c, fmt.Sprintf("Invalid request format: %s", err.Error()))
		return
	}

	// 创建抓取服务实例
	scraperService := services.NewScraperService()

	// 验证URL
	if err := scraperService.ValidateURL(req.URL); err != nil {
		render.BadRequest(c, fmt.Sprintf("Invalid URL: %s", err.Error()))
		return
	}

	// 构建抓取选项
	options := services.ScrapingOptions{
		URL:             req.URL,
		WaitForSelector: req.WaitForSelector,
		RemoveSelectors: req.RemoveSelectors,
		ContentSelector: req.ContentSelector,
		TitleSelector:   req.TitleSelector,
		Timeout:         req.Timeout,
		TakeScreenshot:  req.TakeScreenshot,
		CustomHeaders:   req.CustomHeaders,
		UserAgent:       req.UserAgent,
		ViewportWidth:   req.ViewportWidth,
		ViewportHeight:  req.ViewportHeight,
		WaitTime:        req.WaitTime,
	}

	// 执行抓取
	result, err := scraperService.ScrapeTemplate(c.Request.Context(), options)
	if err != nil {
		render.InternalServerError(c, fmt.Sprintf("Failed to scrape template: %s", err.Error()))
		return
	}

	render.Success(c, result)
}

// getSupportedSelectors returns supported CSS selectors for scraping
func (h *SOPHandler) getSupportedSelectors(c *gin.Context) {
	scraperService := services.NewScraperService()
	selectors := scraperService.GetSupportedSelectors()
	render.Success(c, selectors)
}

import React from 'react';
import { Modal, Progress, Typography, Space, Alert, Button, Spin } from 'antd';
import {
  RobotOutlined,
  LoadingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  StopOutlined
} from '@ant-design/icons';

const { Text, Title } = Typography;

export interface StreamingProgressProps {
  visible: boolean;
  progress: number;
  isStreaming: boolean;
  error: string | null;
  onCancel: () => void;
  onStop?: () => void;
  title?: string;
  description?: string;
}

const StreamingProgress: React.FC<StreamingProgressProps> = ({
  visible,
  progress,
  isStreaming,
  error,
  onCancel,
  onStop,
  title = 'AI正在生成SOP内容',
  description = '请稍候，AI正在根据您提供的信息生成专业的标准操作程序文档...'
}) => {
  const getStatusIcon = () => {
    if (error) {
      return <ExclamationCircleOutlined style={{ color: '#ff4d4f', fontSize: '24px' }} />;
    }
    if (progress >= 100 && !isStreaming) {
      return <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '24px' }} />;
    }
    return <LoadingOutlined style={{ color: '#1890ff', fontSize: '24px' }} />;
  };

  const getStatusText = () => {
    if (error) {
      return '生成失败';
    }
    if (progress >= 100 && !isStreaming) {
      return '生成完成';
    }
    if (progress === 0) {
      return '正在初始化...';
    }
    if (progress < 20) {
      return '正在分析对象信息...';
    }
    if (progress < 40) {
      return '正在构建生成提示...';
    }
    if (progress < 60) {
      return '正在调用AI模型...';
    }
    if (progress < 80) {
      return '正在生成内容...';
    }
    if (progress < 100) {
      return '正在完善文档结构...';
    }
    return '即将完成...';
  };

  const getProgressStatus = (): "normal" | "exception" | "success" => {
    if (error) return "exception";
    if (progress >= 100 && !isStreaming) return "success";
    return "normal";
  };

  const getProgressColor = () => {
    if (error) return '#ff4d4f';
    if (progress >= 100 && !isStreaming) return '#52c41a';
    return '#1890ff';
  };

  return (
    <Modal
      title={
        <Space>
          <RobotOutlined />
          {title}
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      footer={
        <Space>
          {isStreaming && onStop && (
            <Button 
              icon={<StopOutlined />} 
              onClick={onStop}
              danger
            >
              停止生成
            </Button>
          )}
          <Button 
            onClick={onCancel}
            disabled={isStreaming}
            type={error || (!isStreaming && progress >= 100) ? "primary" : "default"}
          >
            {error ? '关闭' : (!isStreaming && progress >= 100) ? '完成' : '取消'}
          </Button>
        </Space>
      }
      closable={!isStreaming}
      maskClosable={!isStreaming}
      width={500}
      centered
    >
      <div style={{ textAlign: 'center', padding: '20px 0' }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* Status Icon */}
          <div>
            {getStatusIcon()}
          </div>

          {/* Description */}
          <Text type="secondary" style={{ fontSize: '14px' }}>
            {description}
          </Text>

          {/* Progress Bar */}
          <div style={{ width: '100%' }}>
            <Progress
              percent={progress}
              status={getProgressStatus()}
              strokeColor={getProgressColor()}
              trailColor="#f0f0f0"
              strokeWidth={8}
              showInfo={true}
              format={(percent) => `${percent}%`}
            />
          </div>

          {/* Status Text */}
          <div>
            <Space>
              {isStreaming && <Spin size="small" />}
              <Text strong style={{ fontSize: '16px' }}>
                {getStatusText()}
              </Text>
            </Space>
          </div>

          {/* Error Alert */}
          {error && (
            <Alert
              message="生成失败"
              description={error}
              type="error"
              showIcon
              style={{ textAlign: 'left' }}
            />
          )}

          {/* Success Message */}
          {!error && progress >= 100 && !isStreaming && (
            <Alert
              message="生成成功"
              description="SOP内容已成功生成并添加到编辑器中，您可以继续编辑或保存文档。"
              type="success"
              showIcon
              style={{ textAlign: 'left' }}
            />
          )}

          {/* Generation Tips */}
          {isStreaming && !error && (
            <div style={{ textAlign: 'left', width: '100%' }}>
              <Alert
                message="生成提示"
                description={
                  <ul style={{ margin: 0, paddingLeft: '20px' }}>
                    <li>AI正在根据您的对象信息生成专业的SOP文档</li>
                    <li>生成过程中内容会实时显示在编辑器中</li>
                    <li>您可以随时停止生成并手动编辑内容</li>
                    <li>生成完成后可以继续修改和完善文档</li>
                  </ul>
                }
                type="info"
                showIcon
              />
            </div>
          )}
        </Space>
      </div>
    </Modal>
  );
};

export default StreamingProgress;

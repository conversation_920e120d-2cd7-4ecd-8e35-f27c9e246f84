import React, { useState, useCallback } from 'react';
import {
  Modal,
  Form,
  Input,
  Button,
  Select,
  Space,
  Card,
  Row,
  Col,
  Tag,
  Tooltip,
  Alert,
  Divider,
  Switch,
  InputNumber,
  DatePicker,
  TimePicker,
  Checkbox,
  Radio,
  message
} from 'antd';
import {
  InfoCircleOutlined,
  PlusOutlined,
  DeleteOutlined,
  SettingOutlined,
  RobotOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  UserOutlined,
  EnvironmentOutlined
} from '@ant-design/icons';
import type { Dayjs } from 'dayjs';

const { TextArea } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface ObjectParameter {
  key: string;
  value: string;
  type: 'text' | 'number' | 'boolean' | 'date' | 'time' | 'select' | 'textarea';
  required?: boolean;
  description?: string;
  options?: string[];
}

interface ObjectInfo {
  name: string;
  type: string;
  environment: string;
  description: string;
  operator: string;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  estimated_duration: number;
  prerequisites: string[];
  custom_parameters: ObjectParameter[];
  context: string;
  expected_outcome: string;
  risk_level: 'low' | 'medium' | 'high';
  backup_required: boolean;
  approval_required: boolean;
  scheduled_time?: Dayjs;
  tags: string[];
}

interface ObjectInfoFormProps {
  visible: boolean;
  onCancel: () => void;
  onSubmit: (objectInfo: ObjectInfo) => void;
  initialValues?: Partial<ObjectInfo>;
  loading?: boolean;
}

const ObjectInfoForm: React.FC<ObjectInfoFormProps> = ({
  visible,
  onCancel,
  onSubmit,
  initialValues,
  loading = false
}) => {
  const [form] = Form.useForm();
  const [customParams, setCustomParams] = useState<ObjectParameter[]>([]);
  const [activeTab, setActiveTab] = useState<'basic' | 'advanced' | 'custom'>('basic');

  // 预定义的对象类型
  const objectTypes = [
    { value: 'server', label: '服务器', icon: '🖥️' },
    { value: 'database', label: '数据库', icon: '🗄️' },
    { value: 'application', label: '应用程序', icon: '📱' },
    { value: 'network', label: '网络设备', icon: '🌐' },
    { value: 'storage', label: '存储设备', icon: '💾' },
    { value: 'security', label: '安全设备', icon: '🔒' },
    { value: 'monitoring', label: '监控系统', icon: '📊' },
    { value: 'backup', label: '备份系统', icon: '💿' },
    { value: 'other', label: '其他', icon: '⚙️' }
  ];

  // 环境类型
  const environments = [
    { value: 'production', label: '生产环境', color: 'red' },
    { value: 'staging', label: '预发布环境', color: 'orange' },
    { value: 'testing', label: '测试环境', color: 'blue' },
    { value: 'development', label: '开发环境', color: 'green' },
    { value: 'disaster_recovery', label: '容灾环境', color: 'purple' }
  ];

  // 紧急程度
  const urgencyLevels = [
    { value: 'low', label: '低', color: 'green', description: '常规维护，可延期' },
    { value: 'medium', label: '中', color: 'blue', description: '正常优先级' },
    { value: 'high', label: '高', color: 'orange', description: '需要尽快处理' },
    { value: 'critical', label: '紧急', color: 'red', description: '立即处理' }
  ];

  // 风险等级
  const riskLevels = [
    { value: 'low', label: '低风险', color: 'green' },
    { value: 'medium', label: '中风险', color: 'orange' },
    { value: 'high', label: '高风险', color: 'red' }
  ];

  React.useEffect(() => {
    if (visible && initialValues) {
      form.setFieldsValue(initialValues);
      if (initialValues.custom_parameters) {
        setCustomParams(initialValues.custom_parameters);
      }
    }
  }, [visible, initialValues, form]);

  const handleSubmit = useCallback(async () => {
    try {
      const values = await form.validateFields();
      const objectInfo: ObjectInfo = {
        ...values,
        custom_parameters: customParams,
        prerequisites: values.prerequisites || [],
        tags: values.tags || []
      };
      onSubmit(objectInfo);
    } catch (error) {
      message.error('请完善必填信息');
    }
  }, [form, customParams, onSubmit]);

  const addCustomParameter = useCallback(() => {
    const newParam: ObjectParameter = {
      key: '',
      value: '',
      type: 'text',
      required: false
    };
    setCustomParams(prev => [...prev, newParam]);
  }, []);

  const updateCustomParameter = useCallback((index: number, field: keyof ObjectParameter, value: any) => {
    setCustomParams(prev => prev.map((param, i) => 
      i === index ? { ...param, [field]: value } : param
    ));
  }, []);

  const removeCustomParameter = useCallback((index: number) => {
    setCustomParams(prev => prev.filter((_, i) => i !== index));
  }, []);

  const renderBasicInfo = () => (
    <Card title={
      <Space>
        <InfoCircleOutlined />
        基本信息
      </Space>
    } size="small">
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="操作对象名称"
            name="name"
            rules={[{ required: true, message: '请输入操作对象名称' }]}
          >
            <Input placeholder="例如：Web服务器-01" prefix={<FileTextOutlined />} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="对象类型"
            name="type"
            rules={[{ required: true, message: '请选择对象类型' }]}
          >
            <Select placeholder="选择对象类型">
              {objectTypes.map(type => (
                <Option key={type.value} value={type.value}>
                  <Space>
                    <span>{type.icon}</span>
                    {type.label}
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="运行环境"
            name="environment"
            rules={[{ required: true, message: '请选择运行环境' }]}
          >
            <Select placeholder="选择运行环境">
              {environments.map(env => (
                <Option key={env.value} value={env.value}>
                  <Tag color={env.color}>{env.label}</Tag>
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="操作人员"
            name="operator"
            rules={[{ required: true, message: '请输入操作人员' }]}
          >
            <Input placeholder="操作人员姓名" prefix={<UserOutlined />} />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        label="对象描述"
        name="description"
        rules={[{ required: true, message: '请输入对象描述' }]}
      >
        <TextArea 
          rows={3} 
          placeholder="详细描述操作对象的功能、配置、重要性等信息"
        />
      </Form.Item>

      <Form.Item
        label="操作背景"
        name="context"
        rules={[{ required: true, message: '请输入操作背景' }]}
      >
        <TextArea 
          rows={3} 
          placeholder="说明为什么需要进行此操作，当前遇到的问题或需求"
        />
      </Form.Item>

      <Form.Item
        label="预期结果"
        name="expected_outcome"
        rules={[{ required: true, message: '请输入预期结果' }]}
      >
        <TextArea 
          rows={2} 
          placeholder="描述操作完成后的预期状态和效果"
        />
      </Form.Item>
    </Card>
  );

  const renderAdvancedSettings = () => (
    <Card title={
      <Space>
        <SettingOutlined />
        高级设置
      </Space>
    } size="small">
      <Row gutter={16}>
        <Col span={8}>
          <Form.Item
            label="紧急程度"
            name="urgency"
            rules={[{ required: true, message: '请选择紧急程度' }]}
          >
            <Select placeholder="选择紧急程度">
              {urgencyLevels.map(level => (
                <Option key={level.value} value={level.value}>
                  <Space>
                    <Tag color={level.color}>{level.label}</Tag>
                    <span style={{ fontSize: '12px', color: '#666' }}>
                      {level.description}
                    </span>
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label="风险等级"
            name="risk_level"
            rules={[{ required: true, message: '请选择风险等级' }]}
          >
            <Select placeholder="选择风险等级">
              {riskLevels.map(risk => (
                <Option key={risk.value} value={risk.value}>
                  <Tag color={risk.color}>{risk.label}</Tag>
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label="预计耗时(分钟)"
            name="estimated_duration"
            rules={[{ required: true, message: '请输入预计耗时' }]}
          >
            <InputNumber 
              min={1} 
              max={1440} 
              placeholder="分钟"
              style={{ width: '100%' }}
              addonAfter={<ClockCircleOutlined />}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="前置条件"
            name="prerequisites"
          >
            <Select
              mode="tags"
              placeholder="输入前置条件，按回车添加"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="标签"
            name="tags"
          >
            <Select
              mode="tags"
              placeholder="添加标签便于分类"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={8}>
          <Form.Item
            label="需要备份"
            name="backup_required"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label="需要审批"
            name="approval_required"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label="计划执行时间"
            name="scheduled_time"
          >
            <DatePicker 
              showTime 
              placeholder="选择执行时间"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  );

  const renderCustomParameters = () => (
    <Card 
      title={
        <Space>
          <SettingOutlined />
          自定义参数
        </Space>
      }
      size="small"
      extra={
        <Button 
          type="dashed" 
          icon={<PlusOutlined />} 
          onClick={addCustomParameter}
          size="small"
        >
          添加参数
        </Button>
      }
    >
      {customParams.length === 0 ? (
        <Alert
          message="暂无自定义参数"
          description="点击右上角按钮添加特定于此操作的自定义参数"
          type="info"
          showIcon
        />
      ) : (
        <Space direction="vertical" style={{ width: '100%' }}>
          {customParams.map((param, index) => (
            <Card key={index} size="small" style={{ backgroundColor: '#fafafa' }}>
              <Row gutter={8} align="middle">
                <Col span={6}>
                  <Input
                    placeholder="参数名"
                    value={param.key}
                    onChange={(e) => updateCustomParameter(index, 'key', e.target.value)}
                  />
                </Col>
                <Col span={4}>
                  <Select
                    value={param.type}
                    onChange={(value) => updateCustomParameter(index, 'type', value)}
                    style={{ width: '100%' }}
                  >
                    <Option value="text">文本</Option>
                    <Option value="number">数字</Option>
                    <Option value="boolean">布尔</Option>
                    <Option value="date">日期</Option>
                    <Option value="select">选择</Option>
                    <Option value="textarea">多行文本</Option>
                  </Select>
                </Col>
                <Col span={6}>
                  <Input
                    placeholder="参数值"
                    value={param.value}
                    onChange={(e) => updateCustomParameter(index, 'value', e.target.value)}
                  />
                </Col>
                <Col span={6}>
                  <Input
                    placeholder="描述(可选)"
                    value={param.description}
                    onChange={(e) => updateCustomParameter(index, 'description', e.target.value)}
                  />
                </Col>
                <Col span={2}>
                  <Space>
                    <Checkbox
                      checked={param.required}
                      onChange={(e) => updateCustomParameter(index, 'required', e.target.checked)}
                    >
                      必填
                    </Checkbox>
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => removeCustomParameter(index)}
                      size="small"
                    />
                  </Space>
                </Col>
              </Row>
            </Card>
          ))}
        </Space>
      )}
    </Card>
  );

  return (
    <Modal
      title={
        <Space>
          <RobotOutlined />
          操作对象信息
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      width={900}
      footer={
        <Space>
          <Button onClick={onCancel}>
            取消
          </Button>
          <Button 
            type="primary" 
            onClick={handleSubmit}
            loading={loading}
            icon={<RobotOutlined />}
          >
            开始生成SOP
          </Button>
        </Space>
      }
      destroyOnClose
    >
      <Alert
        message="AI SOP生成向导"
        description="请详细填写操作对象信息，AI将根据这些信息生成专业的标准操作程序文档。信息越详细，生成的SOP越准确。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <div style={{ marginBottom: 16 }}>
        <Radio.Group 
          value={activeTab} 
          onChange={(e) => setActiveTab(e.target.value)}
          buttonStyle="solid"
        >
          <Radio.Button value="basic">基本信息</Radio.Button>
          <Radio.Button value="advanced">高级设置</Radio.Button>
          <Radio.Button value="custom">自定义参数</Radio.Button>
        </Radio.Group>
      </div>

      <Form
        form={form}
        layout="vertical"
        initialValues={{
          urgency: 'medium',
          risk_level: 'medium',
          estimated_duration: 30,
          backup_required: false,
          approval_required: false
        }}
      >
        {activeTab === 'basic' && renderBasicInfo()}
        {activeTab === 'advanced' && renderAdvancedSettings()}
        {activeTab === 'custom' && renderCustomParameters()}
      </Form>
    </Modal>
  );
};

export default ObjectInfoForm;

import React, { useMemo, useCallback, useState } from 'react';
import { createEditor } from 'prosekit/core';
import { defineBasicExtension } from 'prosekit/basic';
import { definePlaceholder } from 'prosekit/extensions/placeholder';
import { union } from 'prosekit/core';
import { ProseKit, useDocChange } from 'prosekit/react';
import { Button, Tooltip, Divider, Space, message } from 'antd';
import {
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  StrikethroughOutlined,
  CodeOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,
  UndoOutlined,
  RedoOutlined,
  RobotOutlined,
  LoadingOutlined,
  TableOutlined,
  LinkOutlined,
  QuestionCircleOutlined,
  LineOutlined,
  ClearOutlined,
  SaveOutlined,
  DownloadOutlined
} from '@ant-design/icons';

import 'prosekit/basic/style.css';
import './SOPEditor.css';
import ObjectInfoForm from './ObjectInfoForm';

interface ObjectInfo {
  name: string;
  type: string;
  environment: string;
  description: string;
  operator: string;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  estimated_duration: number;
  prerequisites: string[];
  custom_parameters: any[];
  context: string;
  expected_outcome: string;
  risk_level: 'low' | 'medium' | 'high';
  backup_required: boolean;
  approval_required: boolean;
  scheduled_time?: any;
  tags: string[];
}

interface SOPEditorProps {
  value?: string;
  onChange?: (html: string) => void;
  placeholder?: string;
  onAIGenerate?: (objectInfo: ObjectInfo) => Promise<void>;
  onSave?: () => void;
  onExport?: () => void;
  disabled?: boolean;
  className?: string;
}

// 工具栏按钮组件
const ToolbarButton: React.FC<{
  onClick: () => void;
  isActive?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
  title?: string;
  loading?: boolean;
}> = ({ onClick, isActive, disabled, children, title, loading }) => (
  <Tooltip title={title}>
    <Button
      type={isActive ? 'primary' : 'text'}
      size="small"
      onClick={onClick}
      disabled={disabled || loading}
      icon={loading ? <LoadingOutlined /> : children}
      className={`sop-toolbar-button ${isActive ? 'active' : ''}`}
      onMouseDown={(e) => e.preventDefault()}
    />
  </Tooltip>
);

// 工具栏组件
const SOPToolbar: React.FC<{
  editor: any;
  onAIGenerateClick?: () => void;
  onSave?: () => void;
  onExport?: () => void;
  disabled?: boolean;
  isGenerating?: boolean;
}> = ({ editor, onAIGenerateClick, onSave, onExport, disabled, isGenerating }) => {

  return (
    <div className="sop-toolbar">
      <Space size="small">
        {/* 撤销/重做 */}
        <ToolbarButton
          onClick={() => editor.commands.undo()}
          disabled={disabled || !editor.commands.undo.canExec()}
          title="撤销 (Ctrl+Z)"
        >
          <UndoOutlined />
        </ToolbarButton>
        <ToolbarButton
          onClick={() => editor.commands.redo()}
          disabled={disabled || !editor.commands.redo.canExec()}
          title="重做 (Ctrl+Y)"
        >
          <RedoOutlined />
        </ToolbarButton>
        
        <Divider type="vertical" />
        
        {/* 文本格式 */}
        <ToolbarButton
          onClick={() => editor.commands.toggleBold()}
          isActive={editor.marks.bold.isActive()}
          disabled={disabled || !editor.commands.toggleBold.canExec()}
          title="加粗 (Ctrl+B)"
        >
          <BoldOutlined />
        </ToolbarButton>
        <ToolbarButton
          onClick={() => editor.commands.toggleItalic()}
          isActive={editor.marks.italic.isActive()}
          disabled={disabled || !editor.commands.toggleItalic.canExec()}
          title="斜体 (Ctrl+I)"
        >
          <ItalicOutlined />
        </ToolbarButton>
        <ToolbarButton
          onClick={() => editor.commands.toggleUnderline()}
          isActive={editor.marks.underline.isActive()}
          disabled={disabled || !editor.commands.toggleUnderline.canExec()}
          title="下划线 (Ctrl+U)"
        >
          <UnderlineOutlined />
        </ToolbarButton>
        <ToolbarButton
          onClick={() => editor.commands.toggleStrike()}
          isActive={editor.marks.strike.isActive()}
          disabled={disabled || !editor.commands.toggleStrike.canExec()}
          title="删除线"
        >
          <StrikethroughOutlined />
        </ToolbarButton>
        <ToolbarButton
          onClick={() => editor.commands.toggleCode()}
          isActive={editor.marks.code.isActive()}
          disabled={disabled || !editor.commands.toggleCode.canExec()}
          title="代码"
        >
          <CodeOutlined />
        </ToolbarButton>
        
        <Divider type="vertical" />
        
        {/* 标题 */}
        <ToolbarButton
          onClick={() => editor.commands.toggleHeading({ level: 1 })}
          isActive={editor.nodes.heading.isActive({ level: 1 })}
          disabled={disabled || !editor.commands.toggleHeading.canExec({ level: 1 })}
          title="标题1"
        >
          H1
        </ToolbarButton>
        <ToolbarButton
          onClick={() => editor.commands.toggleHeading({ level: 2 })}
          isActive={editor.nodes.heading.isActive({ level: 2 })}
          disabled={disabled || !editor.commands.toggleHeading.canExec({ level: 2 })}
          title="标题2"
        >
          H2
        </ToolbarButton>
        <ToolbarButton
          onClick={() => editor.commands.toggleHeading({ level: 3 })}
          isActive={editor.nodes.heading.isActive({ level: 3 })}
          disabled={disabled || !editor.commands.toggleHeading.canExec({ level: 3 })}
          title="标题3"
        >
          H3
        </ToolbarButton>
        
        <Divider type="vertical" />
        
        {/* 列表 */}
        <ToolbarButton
          onClick={() => editor.commands.toggleList({ kind: 'bullet' })}
          isActive={editor.nodes.list.isActive({ kind: 'bullet' })}
          disabled={disabled || !editor.commands.toggleList.canExec({ kind: 'bullet' })}
          title="无序列表"
        >
          <UnorderedListOutlined />
        </ToolbarButton>
        <ToolbarButton
          onClick={() => editor.commands.toggleList({ kind: 'ordered' })}
          isActive={editor.nodes.list.isActive({ kind: 'ordered' })}
          disabled={disabled || !editor.commands.toggleList.canExec({ kind: 'ordered' })}
          title="有序列表"
        >
          <OrderedListOutlined />
        </ToolbarButton>

        <Divider type="vertical" />

        {/* 高级功能 */}
        <ToolbarButton
          onClick={() => {
            try {
              // 插入表格
              console.log('开始插入表格...');
              const result = editor.commands.insertTable({ row: 2, col: 2, header: true });
              console.log('插入表格结果:', result);
              console.log('编辑器内容:', editor.view.state.doc.toJSON());

              // 强制更新编辑器视图
              editor.view.focus();

              // 触发 onChange 事件
              const newContent = editor.view.state.doc.toString();
              console.log('新内容:', newContent);
              onValueChange?.(newContent);
            } catch (error) {
              console.error('插入表格失败:', error);
            }
          }}
          disabled={disabled}
          title="插入表格"
        >
          <TableOutlined />
        </ToolbarButton>
        <ToolbarButton
          onClick={() => {
            const url = window.prompt('请输入链接地址:');
            const text = window.prompt('请输入链接文本:') || url;
            if (url) {
              editor.commands.addLink({ href: url });
            }
          }}
          disabled={disabled}
          title="插入链接"
        >
          <LinkOutlined />
        </ToolbarButton>
        <ToolbarButton
          onClick={() => {
            editor.commands.insertHorizontalRule();
          }}
          disabled={disabled}
          title="插入分割线"
        >
          <LineOutlined />
        </ToolbarButton>
        <ToolbarButton
          onClick={() => {
            const text = window.prompt('请输入引用内容:');
            if (text) {
              editor.commands.insertBlockquote();
            }
          }}
          disabled={disabled}
          title="插入引用"
        >
          <QuestionCircleOutlined />
        </ToolbarButton>

        <Divider type="vertical" />

        {/* 文档操作 */}
        {onSave && (
          <ToolbarButton
            onClick={onSave}
            disabled={disabled}
            title="保存文档"
          >
            <SaveOutlined />
          </ToolbarButton>
        )}
        {onExport && (
          <ToolbarButton
            onClick={onExport}
            disabled={disabled}
            title="导出文档"
          >
            <DownloadOutlined />
          </ToolbarButton>
        )}
        <ToolbarButton
          onClick={() => {
            if (window.confirm('确定要清空所有内容吗？')) {
              editor.commands.setContent('<p></p>');
            }
          }}
          disabled={disabled}
          title="清空内容"
        >
          <ClearOutlined />
        </ToolbarButton>

        {/* AI生成按钮 */}
        {onAIGenerateClick && (
          <>
            <Divider type="vertical" />
            <ToolbarButton
              onClick={onAIGenerateClick}
              disabled={disabled}
              loading={isGenerating}
              title="AI智能生成内容"
            >
              <RobotOutlined />
            </ToolbarButton>
          </>
        )}
      </Space>
    </div>
  );
};

const SOPEditor: React.FC<SOPEditorProps> = ({
  value = '',
  onChange,
  placeholder = '请输入SOP内容...',
  onAIGenerate,
  onSave,
  onExport,
  disabled = false,
  className = ''
}) => {
  const [isObjectFormVisible, setIsObjectFormVisible] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  // 创建编辑器实例
  const editor = useMemo(() => {
    const extension = union(
      defineBasicExtension(),
      definePlaceholder({ placeholder })
    );

    return createEditor({
      extension,
      defaultContent: value || '<p></p>',
    });
  }, [placeholder]);

  // 监听文档变化
  useDocChange(() => {
    if (onChange && !disabled) {
      const html = editor.getDocHTML();
      onChange(html);
    }
  }, { editor });

  // 处理AI生成
  const handleAIGenerate = useCallback(() => {
    setIsObjectFormVisible(true);
  }, []);

  const handleObjectInfoSubmit = useCallback(async (objectInfo: ObjectInfo) => {
    setIsGenerating(true);
    setIsObjectFormVisible(false);

    try {
      if (onAIGenerate) {
        await onAIGenerate(objectInfo);
        message.success('SOP内容生成成功！');
      }
    } catch (error) {
      message.error('生成失败，请重试');
    } finally {
      setIsGenerating(false);
    }
  }, [onAIGenerate]);

  return (
    <ProseKit editor={editor}>
      <div className={`sop-editor-wrapper ${className} ${disabled ? 'disabled' : ''}`}>
        <SOPToolbar
          editor={editor}
          onAIGenerateClick={handleAIGenerate}
          onSave={onSave}
          onExport={onExport}
          disabled={disabled}
          isGenerating={isGenerating}
        />
        <div
          ref={editor.mount}
          className="sop-editor ProseMirror"
        />
      </div>

      <ObjectInfoForm
        visible={isObjectFormVisible}
        onCancel={() => setIsObjectFormVisible(false)}
        onSubmit={handleObjectInfoSubmit}
        loading={isGenerating}
      />
    </ProseKit>
  );
};

export default SOPEditor;

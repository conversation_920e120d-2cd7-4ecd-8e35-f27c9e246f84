import React, { useState, useCallback } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  Modal, 
  Form, 
  Input, 
  Select, 
  message, 
  Table, 
  Tag, 
  Popconfirm,
  Row,
  Col,
  Typography,
  Divider
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  EyeOutlined,
  FileTextOutlined,
  RobotOutlined
} from '@ant-design/icons';
import { SOPEditor, SOPViewer } from './index';
import './SOPManagement.css';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface SOPDocument {
  id: number;
  title: string;
  content: string;
  category: string;
  status: 'draft' | 'published' | 'archived';
  createdAt: string;
  updatedAt: string;
  author: string;
}

interface SOPTemplate {
  id: number;
  name: string;
  description: string;
  content: string;
  category: string;
}

const SOPManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'documents' | 'templates'>('documents');
  const [documents, setDocuments] = useState<SOPDocument[]>([]);
  const [templates, setTemplates] = useState<SOPTemplate[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isViewModalVisible, setIsViewModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<SOPDocument | SOPTemplate | null>(null);
  const [viewingItem, setViewingItem] = useState<SOPDocument | null>(null);
  const [modalType, setModalType] = useState<'document' | 'template'>('document');
  const [form] = Form.useForm();

  // 模拟数据
  React.useEffect(() => {
    setDocuments([
      {
        id: 1,
        title: '服务器部署标准操作流程',
        content: '<h1>服务器部署标准操作流程</h1><p>本文档描述了服务器部署的标准操作流程...</p>',
        category: '运维',
        status: 'published',
        createdAt: '2024-01-15',
        updatedAt: '2024-01-20',
        author: '张三'
      },
      {
        id: 2,
        title: '数据库备份恢复流程',
        content: '<h1>数据库备份恢复流程</h1><p>本文档描述了数据库备份和恢复的操作流程...</p>',
        category: '数据库',
        status: 'draft',
        createdAt: '2024-01-10',
        updatedAt: '2024-01-18',
        author: '李四'
      }
    ]);

    setTemplates([
      {
        id: 1,
        name: '标准运维流程模板',
        description: '用于创建标准运维操作流程的模板',
        content: '<h1>操作流程标题</h1><h2>1. 准备工作</h2><p>描述准备工作...</p><h2>2. 执行步骤</h2><ol><li>步骤一</li><li>步骤二</li></ol>',
        category: '运维'
      },
      {
        id: 2,
        name: '故障处理模板',
        description: '用于创建故障处理流程的模板',
        content: '<h1>故障处理流程</h1><h2>1. 故障识别</h2><p>描述故障识别方法...</p><h2>2. 处理步骤</h2><ol><li>紧急处理</li><li>根因分析</li></ol>',
        category: '故障处理'
      }
    ]);
  }, []);

  const handleCreateDocument = () => {
    setModalType('document');
    setEditingItem(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleCreateTemplate = () => {
    setModalType('template');
    setEditingItem(null);
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (item: SOPDocument | SOPTemplate, type: 'document' | 'template') => {
    setModalType(type);
    setEditingItem(item);
    setIsModalVisible(true);
    form.setFieldsValue(item);
  };

  const handleView = (document: SOPDocument) => {
    setViewingItem(document);
    setIsViewModalVisible(true);
  };

  const handleDelete = (id: number, type: 'document' | 'template') => {
    if (type === 'document') {
      setDocuments(prev => prev.filter(doc => doc.id !== id));
    } else {
      setTemplates(prev => prev.filter(tpl => tpl.id !== id));
    }
    message.success('删除成功');
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      if (modalType === 'document') {
        if (editingItem) {
          // 更新文档
          setDocuments(prev => prev.map(doc => 
            doc.id === editingItem.id ? { ...doc, ...values, updatedAt: new Date().toISOString().split('T')[0] } : doc
          ));
          message.success('文档更新成功');
        } else {
          // 创建新文档
          const newDoc: SOPDocument = {
            id: Date.now(),
            ...values,
            status: 'draft',
            createdAt: new Date().toISOString().split('T')[0],
            updatedAt: new Date().toISOString().split('T')[0],
            author: '当前用户'
          };
          setDocuments(prev => [...prev, newDoc]);
          message.success('文档创建成功');
        }
      } else {
        if (editingItem) {
          // 更新模板
          setTemplates(prev => prev.map(tpl => 
            tpl.id === editingItem.id ? { ...tpl, ...values } : tpl
          ));
          message.success('模板更新成功');
        } else {
          // 创建新模板
          const newTemplate: SOPTemplate = {
            id: Date.now(),
            ...values
          };
          setTemplates(prev => [...prev, newTemplate]);
          message.success('模板创建成功');
        }
      }
      setIsModalVisible(false);
      setEditingItem(null);
      form.resetFields();
    });
  };

  const handleAIGenerate = useCallback(async (currentContent: string) => {
    // 模拟AI生成内容
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        const aiContent = `
          <h2>AI生成的内容</h2>
          <p>基于当前内容，AI建议添加以下步骤：</p>
          <ol>
            <li>检查系统状态</li>
            <li>备份重要数据</li>
            <li>执行操作</li>
            <li>验证结果</li>
            <li>记录操作日志</li>
          </ol>
          <p><strong>注意事项：</strong>请确保在执行前仔细阅读相关文档。</p>
        `;
        
        const currentFormContent = form.getFieldValue('content') || '';
        form.setFieldsValue({
          content: currentFormContent + aiContent
        });
        resolve();
      }, 2000);
    });
  }, [form]);

  const documentColumns = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: SOPDocument) => (
        <Space>
          <FileTextOutlined />
          <Text strong>{text}</Text>
        </Space>
      )
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      render: (category: string) => <Tag color="blue">{category}</Tag>
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colors = { draft: 'orange', published: 'green', archived: 'gray' };
        const labels = { draft: '草稿', published: '已发布', archived: '已归档' };
        return <Tag color={colors[status as keyof typeof colors]}>{labels[status as keyof typeof labels]}</Tag>;
      }
    },
    {
      title: '作者',
      dataIndex: 'author',
      key: 'author'
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt'
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: SOPDocument) => (
        <Space>
          <Button 
            type="text" 
            icon={<EyeOutlined />} 
            onClick={() => handleView(record)}
            title="查看"
          />
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record, 'document')}
            title="编辑"
          />
          <Popconfirm
            title="确定要删除这个文档吗？"
            onConfirm={() => handleDelete(record.id, 'document')}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="text" 
              danger 
              icon={<DeleteOutlined />}
              title="删除"
            />
          </Popconfirm>
        </Space>
      )
    }
  ];

  const templateColumns = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => (
        <Space>
          <FileTextOutlined />
          <Text strong>{text}</Text>
        </Space>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      render: (category: string) => <Tag color="purple">{category}</Tag>
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: SOPTemplate) => (
        <Space>
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record, 'template')}
            title="编辑"
          />
          <Popconfirm
            title="确定要删除这个模板吗？"
            onConfirm={() => handleDelete(record.id, 'template')}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="text" 
              danger 
              icon={<DeleteOutlined />}
              title="删除"
            />
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div className="sop-management">
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
          <Col>
            <Title level={3} style={{ margin: 0 }}>
              SOP 标准操作流程管理
            </Title>
            <Text type="secondary">
              管理标准操作流程文档和模板，支持AI智能生成
            </Text>
          </Col>
          <Col>
            <Space>
              <Button
                type={activeTab === 'documents' ? 'primary' : 'default'}
                onClick={() => setActiveTab('documents')}
              >
                文档管理
              </Button>
              <Button
                type={activeTab === 'templates' ? 'primary' : 'default'}
                onClick={() => setActiveTab('templates')}
              >
                模板管理
              </Button>
            </Space>
          </Col>
        </Row>

        <Divider />

        {activeTab === 'documents' && (
          <>
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <Title level={4} style={{ margin: 0 }}>SOP文档</Title>
              </Col>
              <Col>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={handleCreateDocument}
                >
                  创建文档
                </Button>
              </Col>
            </Row>
            <Table
              columns={documentColumns}
              dataSource={documents}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </>
        )}

        {activeTab === 'templates' && (
          <>
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <Title level={4} style={{ margin: 0 }}>SOP模板</Title>
              </Col>
              <Col>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={handleCreateTemplate}
                >
                  创建模板
                </Button>
              </Col>
            </Row>
            <Table
              columns={templateColumns}
              dataSource={templates}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </>
        )}
      </Card>

      {/* 创建/编辑模态框 */}
      <Modal
        title={`${editingItem ? '编辑' : '创建'}${modalType === 'document' ? 'SOP文档' : 'SOP模板'}`}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingItem(null);
          form.resetFields();
        }}
        width={1000}
        okText="保存"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          {modalType === 'document' ? (
            <>
              <Form.Item
                name="title"
                label="文档标题"
                rules={[{ required: true, message: '请输入文档标题' }]}
              >
                <Input placeholder="请输入文档标题" />
              </Form.Item>
              <Form.Item
                name="category"
                label="分类"
                rules={[{ required: true, message: '请选择分类' }]}
              >
                <Select placeholder="请选择分类">
                  <Select.Option value="运维">运维</Select.Option>
                  <Select.Option value="数据库">数据库</Select.Option>
                  <Select.Option value="网络">网络</Select.Option>
                  <Select.Option value="安全">安全</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Select.Option value="draft">草稿</Select.Option>
                  <Select.Option value="published">已发布</Select.Option>
                  <Select.Option value="archived">已归档</Select.Option>
                </Select>
              </Form.Item>
            </>
          ) : (
            <>
              <Form.Item
                name="name"
                label="模板名称"
                rules={[{ required: true, message: '请输入模板名称' }]}
              >
                <Input placeholder="请输入模板名称" />
              </Form.Item>
              <Form.Item
                name="description"
                label="模板描述"
                rules={[{ required: true, message: '请输入模板描述' }]}
              >
                <TextArea rows={3} placeholder="请输入模板描述" />
              </Form.Item>
              <Form.Item
                name="category"
                label="分类"
                rules={[{ required: true, message: '请选择分类' }]}
              >
                <Select placeholder="请选择分类">
                  <Select.Option value="运维">运维</Select.Option>
                  <Select.Option value="故障处理">故障处理</Select.Option>
                  <Select.Option value="部署">部署</Select.Option>
                  <Select.Option value="监控">监控</Select.Option>
                </Select>
              </Form.Item>
            </>
          )}
          <Form.Item
            name="content"
            label={
              <Space>
                <span>内容</span>
                <Button 
                  type="link" 
                  size="small" 
                  icon={<RobotOutlined />}
                  style={{ padding: 0 }}
                >
                  AI智能生成
                </Button>
              </Space>
            }
            rules={[{ required: true, message: '请输入内容' }]}
          >
            <div style={{ height: '400px' }}>
              <SOPEditor
                value={form.getFieldValue('content')}
                onChange={(html) => form.setFieldsValue({ content: html })}
                onAIGenerate={handleAIGenerate}
                placeholder={`请输入${modalType === 'document' ? '文档' : '模板'}内容...`}
              />
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* 查看文档模态框 */}
      <Modal
        title={viewingItem?.title}
        open={isViewModalVisible}
        onCancel={() => {
          setIsViewModalVisible(false);
          setViewingItem(null);
        }}
        footer={null}
        width={800}
      >
        {viewingItem && (
          <SOPViewer content={viewingItem.content} />
        )}
      </Modal>
    </div>
  );
};

export default SOPManagement;
